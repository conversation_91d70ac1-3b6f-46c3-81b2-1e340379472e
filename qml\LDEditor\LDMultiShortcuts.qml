import QtQuick 2.15
import QtQuick.Controls 2.15

Item {

    // 复制
    function copy()
    {
        const content = {
            type: "copy",
            from: fileKey,
            deviceName: deviceName
        }

        const data = {
            source: "content",
            type: activeFocusItem,
            list: []
        }

        if(activeFocusItem === "NETWORK")
        {
            const netWork = netWorkList.find(netWork => netWork.Number === netWorkNumber)
            if(netWork)
            {
                data.list.push(netWork)
            }
        } 
        else if(activeFocusItem === "BLOCK")
        {
            if(currentSelectBolckData)
            {
                data.list.push(currentSelectBolckData)
            }
        }

        content.data = data
        
        return content
    }

    // 剪切
    function shear()
    {
        const content = {
            type: "shear",
            from: fileKey,
            deviceName: deviceName
        }

        const data = {
            source: "content",
            type: activeFocusItem,
            list: []
        }

        if(activeFocusItem === "NETWORK")
        {
            const netWork = netWorkList.find(netWork => netWork.Number === netWorkNumber)
            if(netWork)
            {
                data.list.push(netWork)
            }
        } 
        else if(activeFocusItem === "BLOCK" && isCanDelete())
        {
            if(currentSelectBolckData)
            {
                data.list.push(currentSelectBolckData)
            }
        }

        content.data = data

        return content
    }

    // 右粘贴
    function rightPaste(handler)
    {
        const shouldHandlePaste = activeFocusItem === "NETWORK" || 
                                 (activeFocusItem === "BLOCK" && isCanPaste())

        if (shouldHandlePaste)
        {
            const content = handler(fileType, deviceName, activeFocusItem)
        }
    }

    // 左粘贴
    function leftPaste(handler)
    {
        if(activeFocusItem === "BLOCK" && isCanPaste(0))
        {
            const content = handler(fileType, deviceName, activeFocusItem)
        }
    }

    // 下粘贴
    function lowerPaste(handler)
    {
        if(activeFocusItem === "BLOCK" && isCanPaste(2))
        {
            const content = handler(fileType, deviceName, activeFocusItem)
        }
    }

    // 撤销
    function undo(handler)
    {
        applySnapshot(handler, false)
    }

    // 反撤销
    function redo(handler)
    {
        applySnapshot(handler, true)
    }

    // 应用快照
    function applySnapshot(handler, direction)
    {
        const snapshot = handler(fileKey, deviceName, direction)
        
        if(snapshot && Object.keys(snapshot).length > 0)
        {
            ldManage.saveFile(fileKey, snapshot.content)
            // 需要重新获取网络中的块元件,防止后端对块元件进行修复导致不一致
            analysisNetWorkData(populateNetWorkConnections(ldManage.getAllInfo(control.fileKey)))
            updateFileFlag(fileKey, true)
        }
    }

    // 删除
    function del()
    {
        if(activeFocusItem === "NETWORK")
        {
            // 删除网络
            ldManage.deleteNetwork(fileKey, netWorkNumber) 
            ldManage.fixLDFileConnections(fileKey)
        }
        else if(activeFocusItem === "BLOCK" && isCanDelete())
        {
            // 删除块元件
            deleteBlock()
        }
        else if(activeFocusItem === "PIN" && currentSelectBlockType === "input" && isAdvance())
        {
            ldManage.componentDeleteConnector(fileKey, currentSelectBolckData.ParentId, currentSelectBolckData.PinId)
            ldManage.fixLDFileConnections(fileKey)
        }
    }

    // 多输入
    function multipleInput()
    {
        if(currentSelectBlockType &&
           currentSelectBlockType === "advance" &&
           isAdvance())
        {
            if(!currentSelectBolckData.connectors)
            {
                return
            }

            // 所有引脚
            const connectors = currentSelectBolckData.connectors

            // 最大输入引脚id
            let maxInputPinId = 0

            for(let cIndex = 0; cIndex < connectors.length; cIndex++)
            {
                const connector = connectors[cIndex]

                if(connector.Direction.toLowerCase() === "input" && connector.PinId > maxInputPinId)
                {
                    maxInputPinId = connector.PinId
                }
            }

            ldManage.InsertConnectorAndVariable(fileKey, currentSelectBolckData.Number, maxInputPinId)
            ldManage.fixLDFileConnections(fileKey)
        }
        else if(isAdvance())
        {
            ldManage.InsertConnectorAndVariable(fileKey, currentSelectBolckData.ParentId, currentSelectBolckData.PinId)
            ldManage.fixLDFileConnections(fileKey)
        }
    }

    // 多输出
    function multipleOutput()
    {
        if(currentSelectBlockType &&
           currentSelectBlockType === "advance" &&
           isAdvanceAndMove())
        {
            if(!currentSelectBolckData.connectors)
            {
                return
            }

            // 所有引脚
            const connectors = currentSelectBolckData.connectors

            // 最小输出引脚id
            let minOutputPinId = 0

            for(let cIndex = 0; cIndex < connectors.length; cIndex++)
            {
                const connector = connectors[cIndex]

                if(connector.Direction.toLowerCase() === "output" && connector.PinId < minOutputPinId)
                {
                    minOutputPinId = connector.PinId
                }
            }

            ldManage.InsertConnectorAndVariable(fileKey, currentSelectBolckData.Number, minOutputPinId)
            ldManage.fixLDFileConnections(fileKey)
        }
        else if(isAdvanceAndMove())
        {
            ldManage.InsertConnectorAndVariable(fileKey, currentSelectBolckData.ParentId, currentSelectBolckData.PinId)
            ldManage.fixLDFileConnections(fileKey)
        }
    }

    // 置反
    function reverse()
    {
        if(currentSelectBlockType &&
            (currentSelectBlockType === "en" ||
            currentSelectBlockType === "input"))
        {
            if(currentSelectBolckData.DataType.toLowerCase() === "bool")
            {
                ldManage.modifyConnectorNegated(fileKey, currentSelectBolckData.ParentId, currentSelectBolckData.PinId)
                ldManage.fixLDFileConnections(fileKey)
            }
        }
        // 选中的块元件非Function、FunctionBlock、advance、set0、set1才能置反
        else if(currentSelectBlockType &&
            currentSelectBlockType !== "func" && 
            currentSelectBlockType !== "fb" && 
            currentSelectBlockType !== "advance" &&
            currentSelectBlockType !== "set0" &&
            currentSelectBlockType !== "set1")
        {
            ldManage.modifyConnectorNegated(fileKey, currentSelectBolckData.Number, currentSelectBolckData.PinId)
            ldManage.fixLDFileConnections(fileKey)
        }
    }

    // 置位/复位
    function setOrReset()
    {
        if(currentSelectBlockType &&
            (currentSelectBlockType === "coil" ||
            currentSelectBlockType === "set1" ||
            currentSelectBlockType === "set0")
            )
        {
            if(currentSelectBlockType === "coil")
            {
                // 置位 set1
                ldManage.setCoilToSet1(fileKey, currentSelectBolckData.Number)
            }
            else if(currentSelectBlockType === "set1")
            {
                // 置位 set0
                ldManage.resetCoilToSet0(fileKey, currentSelectBolckData.Number)
            }
            else if(currentSelectBlockType === "set1" || 
                    currentSelectBlockType === "set0")
            {
                // 复位
                ldManage.resetToCoil(fileKey, currentSelectBolckData.Number)
            }

            ldManage.fixLDFileConnections(fileKey)
        }
    }

    // 添加快照内容
    function addSnapshot(content)
    {
        const snapshot = {
            content,
            deviceName,
            fileKey
        }

        shortcutController.addSnapshot(snapshot)
    }
}