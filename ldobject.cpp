﻿#include "ldobject.h"
#include <QBuffer>

void LDConnection::toXml(QDomDocument &doc, QDomElement &pNode)
{
    QDomElement node = doc.createElement("Connection");
    node.setAttribute("InstanceName", InstanceName);
    node.setAttribute("SourceDataType", SourceDataType);
    node.setAttribute("SourceComponentNumber", SourceComponentNumber);
    node.setAttribute("SourcePinId", SourcePinId);

    node.setAttribute("TargetDataType", TargetDataType);
    node.setAttribute("TargetComponentNumber", TargetComponentNumber);
    node.setAttribute("TargetPinId", TargetPinId);

    node.setAttribute("PlotPath", PlotPath);
    node.setAttribute("Length", Length);
    node.setAttribute("Visible", Visible ? 1 : 0);

    node.setAttribute("VarName", VarName);
    node.setAttribute("VarOwned", VarOwned);
    node.setAttribute("TaskName", TaskName);
    node.setAttribute("TaskOrderNumber", TaskOrderNumber);
    node.setAttribute("SourceConnectIndex", SourceConnectIndex);
    node.setAttribute("TargetConnectIndex", TargetConnectIndex);

    pNode.appendChild(node);
}

void LDConnection::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    InstanceName = eNode.attribute("InstanceName");
    SourceDataType = eNode.attribute("SourceDataType");
    SourceComponentNumber = eNode.attribute("SourceComponentNumber").toInt();
    SourcePinId = eNode.attribute("SourcePinId").toInt();

    TargetDataType = eNode.attribute("TargetDataType");
    TargetComponentNumber = eNode.attribute("TargetComponentNumber").toInt();
    TargetPinId = eNode.attribute("TargetPinId").toInt();

    PlotPath = eNode.attribute("PlotPath");
    Length = eNode.attribute("Length").toUInt();
    Visible = eNode.attribute("Visible").toUInt() > 0 ? true : false;

    VarName = eNode.attribute("VarName");
    VarOwned = eNode.attribute("VarOwned");
    TaskName = eNode.attribute("TaskName", "");
    TaskOrderNumber = eNode.attribute("TaskOrderNumber", "9999").toInt();

    SourceConnectIndex = eNode.attribute("SourceConnectIndex").toInt();
    TargetConnectIndex = eNode.attribute("TargetConnectIndex").toInt();
}

QJsonObject LDConnection::toJsonObject()
{
    QJsonObject obj;
    obj["InstanceName"] = InstanceName;
    obj["SourceDataType"] = SourceDataType;
    obj["SourceComponentNumber"] = SourceComponentNumber;
    obj["SourcePinId"] = SourcePinId;

    obj["TargetDataType"] = TargetDataType;
    obj["TargetComponentNumber"] = TargetComponentNumber;
    obj["TargetPinId"] = TargetPinId;

    obj["PlotPath"] = PlotPath;
    obj["Length"] = Length;
    obj["VarName"] = VarName;
    obj["VarOwned"] = VarOwned;
    obj["TaskName"] = TaskName;
    obj["TaskOrderNumber"] = TaskOrderNumber;
    obj["Visible"] = Visible;
    obj["SourceConnectIndex"] = SourceConnectIndex;
    obj["TargetConnectIndex"] = TargetConnectIndex;

    return obj;
}

QString LDConnection::getKey()
{
    return QString::number(SourceComponentNumber) + "-" + QString::number(SourcePinId) + "-" +
           QString::number(TargetComponentNumber) + "-" + QString::number(TargetPinId);
}

void LDConnections::toXml(QDomDocument &doc, QDomElement &pNode)
{
    QDomElement node = doc.createElement("Connections");

    for (int i = 0; i < connectionList.size(); i++)
    {
        connectionList[i]->toXml(doc, node);
    }
    pNode.appendChild(node);
}

void LDConnections::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    connectionList.clear();
    QDomNodeList childs = eNode.childNodes();
    for (int i = 0; i < childs.count(); i++)
    {
        QDomNode childNode = childs.at(i);
        if (childNode.isElement())
        {
            QSharedPointer<LDConnection> conn = QSharedPointer<LDConnection>(new LDConnection());
            conn->fromXml(childNode);
            connectionList << conn;
        }
    }
}

QJsonArray LDConnections::toJsonArray()
{
    QJsonArray ary;
    for (int i = 0; i < connectionList.size(); i++)
    {
        ary.append(connectionList[i]->toJsonObject());
    }
    return ary;
}

void LDConnector::toXml(QDomDocument &doc, QDomElement &pNode)
{
    QDomElement node = doc.createElement("Connector");
    node.setAttribute("InstanceName", InstanceName);
    node.setAttribute("PinId", PinId);
    node.setAttribute("NewAdd", NewAdd ? 1 : 0);
    node.setAttribute("Name", Name);
    node.setAttribute("DataType", DataType);
    node.setAttribute("SupportChangeDataType", SupportChangeDataType ? 1 : 0);
    node.setAttribute("DataTypeGroup", DataTypeGroup);
    node.setAttribute("Negated", Negated ? 1 : 0);
    node.setAttribute("Direction", Direction);
    node.setAttribute("Comment", Comment);
    node.setAttribute("FastSignal", FastSignal ? 1 : 0);
    node.setAttribute("InitValue", InitValue);
    node.setAttribute("isInitVar", isInitVar ? 1 : 0);
    node.setAttribute("XPos", XPos);
    node.setAttribute("YPos", YPos);

    node.setAttribute("Visible", Visible ? 1 : 0);

    node.setAttribute("IsLogical", IsLogical ? 1 : 0);

    node.setAttribute("ChildNumber", ChildNumber);

    pNode.appendChild(node);
}

void LDConnector::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    InstanceName = eNode.attribute("InstanceName");
    PinId = eNode.attribute("PinId").toInt();
    NewAdd = eNode.attribute("NewAdd").toInt() > 0 ? true : false;
    Name = eNode.attribute("Name");
    DataType = eNode.attribute("DataType");
    SupportChangeDataType = eNode.attribute("SupportChangeDataType").toInt() > 0 ? true : false;
    DataTypeGroup = eNode.attribute("DataTypeGroup");
    Negated = eNode.attribute("Negated").toInt() > 0 ? true : false;
    Direction = eNode.attribute("Direction");
    Comment = eNode.attribute("Comment");
    InitValue = eNode.attribute("InitValue");
    FastSignal = eNode.attribute("FastSignal").toInt() > 0 ? true : false;
    isInitVar = eNode.attribute("isInitVar").toInt() > 0 ? true : false;

    XPos = eNode.attribute("XPos").toInt();
    YPos = eNode.attribute("YPos").toInt();

    Visible = eNode.attribute("Visible", "1").toInt() > 0 ? true : false;

    IsLogical = eNode.attribute("IsLogical", "0").toInt() > 0 ? true : false;

    ChildNumber = eNode.attribute("ChildNumber", "-1").toInt();
}

QJsonObject LDConnector::toJsonObject()
{
    QJsonObject obj;
    obj["InstanceName"] = InstanceName;
    obj["PinId"] = PinId;
    obj["NewAdd"] = NewAdd;
    obj["Name"] = Name;
    obj["DataType"] = DataType;
    obj["SupportChangeDataType"] = SupportChangeDataType;
    obj["DataTypeGroup"] = DataTypeGroup;
    obj["Negated"] = Negated;
    obj["Direction"] = Direction;
    obj["FastSignal"] = FastSignal;
    obj["isInitVar"] = isInitVar;
    obj["InitValue"] = InitValue;
    obj["XPos"] = XPos;
    obj["YPos"] = YPos;
    obj["Comment"] = Comment;
    obj["Visible"] = Visible;
    obj["IsLogical"] = IsLogical;
    obj["ChildNumber"] = ChildNumber;

    return obj;
}

void LDComponent::toXml(QDomDocument &doc, QDomElement &pNode)
{
    QDomElement node = doc.createElement("Component");
    node.setAttribute("Name", Name);
    node.setAttribute("InstanceName", InstanceName);
    node.setAttribute("NetworkNumber", NetworkNumber);
    node.setAttribute("Enable", Enable ? 1 : 0);
    node.setAttribute("Number", Number);
    node.setAttribute("TaskName", TaskName);
    node.setAttribute("TaskOrderNumber", TaskOrderNumber);
    node.setAttribute("Type", Type);
    node.setAttribute("ChildType", ChildType);
    node.setAttribute("AuxContent", AuxContent);
    node.setAttribute("AuxContent1", AuxContent1);
    node.setAttribute("DataType_Local", DataType_Local);
    node.setAttribute("Source", Source);

    node.setAttribute("Comment", Comment);
    node.setAttribute("SupportsInPinType", SupportsInPinType);
    node.setAttribute("SupportsOutPinType", SupportsOutPinType);
    node.setAttribute("SupportsInPinAdd", SupportsInPinAdd ? 1 : 0);
    node.setAttribute("SupportsOutPinAdd", SupportsOutPinAdd ? 1 : 0);

    node.setAttribute("XPos", XPos);
    node.setAttribute("YPos", YPos);
    node.setAttribute("Width", Width);
    node.setAttribute("Height", Height);
    node.setAttribute("MinWidth", MinWidth);
    node.setAttribute("Visible", Visible ? 1 : 0);
    node.setAttribute("LeftOrRight", LeftOrRight);

    node.setAttribute("ParentNumber", ParentNumber);
    node.setAttribute("ParentPinId", ParentPinId);
    node.setAttribute("ParentPinDataType", ParentPinDataType);

    for (int i = 0; i < connectorList.size(); i++)
    {
        connectorList[i]->toXml(doc, node);
    }
    pNode.appendChild(node);
}

void LDComponent::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    Name = eNode.attribute("Name");
    InstanceName = eNode.attribute("InstanceName");
    NetworkNumber = eNode.attribute("NetworkNumber").toInt();
    Enable = eNode.attribute("Enable").toInt() > 0 ? true : false;
    Number = eNode.attribute("Number").toInt();
    Type = eNode.attribute("Type");
    ChildType = eNode.attribute("ChildType");
    AuxContent = eNode.attribute("AuxContent");
    AuxContent1 = eNode.attribute("AuxContent1", "");
    DataType_Local = eNode.attribute("DataType_Local");
    Source = eNode.attribute("Source");
    Comment = eNode.attribute("Comment");
    TaskName = eNode.attribute("TaskName");
    TaskOrderNumber = eNode.attribute("TaskOrderNumber").toInt();
    SupportsInPinType = eNode.attribute("SupportsInPinType");
    SupportsOutPinType = eNode.attribute("SupportsOutPinType");
    SupportsInPinAdd = eNode.attribute("SupportsInPinAdd").toInt() > 0 ? true : false;
    SupportsOutPinAdd = eNode.attribute("SupportsOutPinAdd").toInt() > 0 ? true : false;

    XPos = eNode.attribute("XPos").toInt();
    YPos = eNode.attribute("YPos").toInt();
    Width = eNode.attribute("Width").toInt();
    Height = eNode.attribute("Height").toInt();
    MinWidth = eNode.attribute("MinWidth").toInt();
    Visible = eNode.attribute("Visible", "1").toInt() > 0 ? true : false;
    LeftOrRight = eNode.attribute("LeftOrRight", "1").toInt();

    ParentNumber = eNode.attribute("ParentNumber", "0").toInt();
    ParentPinId = eNode.attribute("ParentPinId", "0").toInt();
    ParentPinDataType = eNode.attribute("ParentPinDataType", "");

    if (Width <= MinWidth)
    {
        Width = MinWidth;
    }
    if (Height <= 1)
    {
        Height = 1;
    }

    InputPinNum = 0;
    OutputPinNum = 0;

    connectorList.clear();

    QDomNodeList childs = eNode.childNodes();
    for (int i = 0; i < childs.count(); i++)
    {
        QDomNode childNode = childs.at(i);
        if (childNode.isElement())
        {
            QSharedPointer<LDConnector> connector = QSharedPointer<LDConnector>(new LDConnector());
            connector->fromXml(childNode);
            connectorList << connector;
        }
    }

    // 更新引脚索引
    LDComponent::setPinPosOffset();
    // 更新块中类型
    LDComponent::updateDataTypeLocal();
}

QJsonObject LDComponent::toJsonObject()
{
    QJsonObject obj;

    obj["Name"] = Name;
    obj["InstanceName"] = InstanceName;
    obj["NetworkNumber"] = NetworkNumber;
    obj["Enable"] = Enable;
    obj["Number"] = Number;
    obj["TaskName"] = TaskName;
    obj["TaskOrderNumber"] = TaskOrderNumber;
    obj["Type"] = Type;
    obj["ChildType"] = ChildType;
    obj["AuxContent"] = AuxContent;
    obj["AuxContent1"] = AuxContent1;
    obj["DataType_Local"] = DataType_Local;
    obj["Source"] = Source;
    obj["InputPinNum"] = InputPinNum;
    obj["OutputPinNum"] = OutputPinNum;
    obj["SupportsInPinType"] = SupportsInPinType;
    obj["SupportsOutPinType"] = SupportsOutPinType;
    obj["SupportsInPinAdd"] = SupportsInPinAdd;
    obj["SupportsOutPinAdd"] = SupportsOutPinAdd;
    obj["XPos"] = XPos;
    obj["YPos"] = YPos;
    obj["Height"] = Height;
    obj["Width"] = Width;
    obj["MinWidth"] = MinWidth;
    obj["Comment"] = Comment;
    obj["Visible"] = Visible;
    obj["LeftOrRight"] = LeftOrRight;
    obj["ParentNumber"] = ParentNumber;
    obj["ParentPinId"] = ParentPinId;
    obj["ParentPinDataType"] = ParentPinDataType;

    // 带入
    QJsonArray connectors;
    // 获取引脚
    for (QSharedPointer<LDConnector> &connector : connectorList)
    {
        QJsonObject objconnector = connector->toJsonObject();

        connectors.append(objconnector);
    }
    obj["connectors"] = connectors;
    return obj;
}

void LDComponent::sortPinList()
{
    // 按connectors的PinId排序
    std::sort(connectorList.begin(), connectorList.end(),
              [](QSharedPointer<LDConnector> &a, QSharedPointer<LDConnector> &b) { return a->PinId < b->PinId; });
}

// 重新计算所有块的高度和引脚的位置偏移
void LDComponent::setPinPosOffset()
{
    // 获取全部引脚值
    if (connectorList.size() > 0)
    {
        // 先进行一次引脚排序
        sortPinList();
        int inCount = 0, outCount = 0;
        // 遍历所有引脚 得到输入数量 和输出数量
        for (QSharedPointer<LDConnector> &it : connectorList)
        {
            if (it->Direction == "Input")
            {
                inCount++;
            }
            else if (it->Direction == "Output")
            {
                outCount++;
            }
        }
        InputPinNum = inCount;
        OutputPinNum = outCount;
        // 偏移高度
        int offHeight = 0;
        if (Type == "Block")
        {
            offHeight = 3;
            // 更新该块的高度
            int minHeight = offHeight + (inCount > outCount ? inCount : outCount);
            Height = minHeight;
        }

        // 计算每个输入输出引脚的地址偏移位置 模块左上角为起始点0,0
        for (QSharedPointer<LDConnector> &it : connectorList)
        {
            if (it->Direction == "Input")
            {
                if (Type == "Block")
                {
                    it->XPos = 0;
                    it->YPos = 2 + it->PinId;
                }
                else
                {
                    it->XPos = 0;
                    it->YPos = -1 + it->PinId;
                }
            }
            else
            {
                if (Type == "Block")
                {
                    it->XPos = Width;
                    it->YPos = 2 + qAbs(it->PinId);
                }
                else
                {
                    it->XPos = Width;
                    it->YPos = -1 + qAbs(it->PinId);
                }
            }
            // qDebug() << "block1:" << SoleId << it.PinId << it.XOffset << it.YOffset;
        }
    }
}

// 更新块中类型
void LDComponent::updateDataTypeLocal()
{
    QMap<QString, QString> map;
    for (QSharedPointer<LDConnector> &conn : connectorList)
    {
        if (conn->SupportChangeDataType)
        {
            map.insert(conn->DataTypeGroup, conn->DataType);
        }
    }
    if (map.size() > 0)
    {
        QList<QString> keys = map.keys();
        qSort(keys.begin(), keys.end());
        QString newDataType;
        int t = 0;
        for (QString key : keys)
        {
            if (t == 0)
            {
                newDataType = map[key];
            }
            else
            {
                newDataType += "," + map[key];
            }
            t++;
        }
        DataType_Local = newDataType;
    }
    else
    {
        DataType_Local = "";
    }
}

// 根据引脚编号搜索引脚
QSharedPointer<LDConnector> LDComponent::searchConnectorFromPinId(int pinId)
{
    for (QSharedPointer<LDConnector> &con_ptr : connectorList)
    {
        if (con_ptr->PinId == pinId)
        {
            return con_ptr;
        }
    }
    return nullptr;
}

// 写入网络
void LDNetwork::toXml(QDomDocument &doc, QDomElement &pNode)
{
    QDomElement node = doc.createElement("Network");
    node.setAttribute("Number", Number);
    node.setAttribute("Type", Type);
    node.setAttribute("Label", Label);
    node.setAttribute("Enable", Enable ? 1 : 0);
    node.setAttribute("Comment", Comment);

    pNode.appendChild(node);
}

// 从xml读取网络
void LDNetwork::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    Number = eNode.attribute("Number").toInt();
    Type = eNode.attribute("Type");
    Label = eNode.attribute("Label");
    Enable = eNode.attribute("Enable").toInt() > 0 ? true : false;
    Comment = eNode.attribute("Comment");
}

// 写入网络
QJsonObject LDNetwork::toJsonObject()
{
    QJsonObject obj;
    obj["Type"] = Type;
    obj["Enable"] = Enable;
    obj["Label"] = Label;
    obj["Number"] = Number;
    obj["Height"] = Height;
    obj["Comment"] = Comment;
    return obj;
}

void LDComponents::toXml(QDomDocument &doc, QDomElement &pNode)
{
    QDomElement node = doc.createElement("Components");

    for (auto &com : componentMap)
    {
        com->toXml(doc, node);
    }
    pNode.appendChild(node);
}

void LDComponents::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();

    componentMap.clear();

    QDomNodeList childs = eNode.childNodes();
    for (int i = 0; i < childs.count(); i++)
    {
        QDomNode childNode = childs.at(i);
        if (childNode.isElement())
        {
            QSharedPointer<LDComponent> comp = QSharedPointer<LDComponent>(new LDComponent());
            comp->fromXml(childNode);
            componentMap.insert(comp->Number, comp);
        }
    }
}

QJsonArray LDComponents::toJsonArray()
{
    QJsonArray ary;
    for (auto &com : componentMap)
    {
        ary.append(com->toJsonObject());
    }
    return ary;
}

void LDNetworks::toXml(QDomDocument &doc, QDomElement &pNode)
{
    QDomElement node = doc.createElement("Networks");

    for (auto nw : networkMap)
    {
        nw->toXml(doc, node);
    }
    pNode.appendChild(node);
}

void LDNetworks::fromXml(QDomNode node)
{
    QDomElement eNode = node.toElement();
    networkMap.clear();

    QDomNodeList childs = eNode.childNodes();
    for (int i = 0; i < childs.count(); i++)
    {
        QDomNode childNode = childs.at(i);
        if (childNode.isElement())
        {
            QSharedPointer<LDNetwork> net = QSharedPointer<LDNetwork>(new LDNetwork());
            net->fromXml(childNode);
            networkMap.insert(net->Number, net);
        }
    }
}

void LDNetworks::sortNetworkList()
{
    QMap<int, QSharedPointer<LDNetwork>> newMap;

    for (auto nw : networkMap)
    {
        newMap.insert(nw->Number, nw);
    }

    networkMap = newMap;
}

QJsonArray LDNetworks::toJsonArray()
{
    QJsonArray ary;
    for (auto nw : networkMap)
    {
        ary.append(nw->toJsonObject());
    }
    return ary;
}

void LDFile::toXml(QString path)
{
    // 打开或创建文件
    QFile file(path);
    if (!file.open(QFile::WriteOnly | QFile::Truncate))
    {
        return;
    }

    // 清理无效链接
    clearInvalidConnections();

    // 重新计算下页面宽度和高度
    reSize();

    QDomDocument doc;
    // 写入xml头部
    QDomProcessingInstruction instruction; // 添加处理命令
    instruction = doc.createProcessingInstruction("xml", "version=\"1.0\" encoding=\"UTF-8\"");
    doc.appendChild(instruction);
    // 添加根节点
    QDomElement root = doc.createElement("File");
    root.setAttribute("Name", Name);
    root.setAttribute("Type", Type);
    root.setAttribute("Code", Code);
    root.setAttribute("Version", Version);
    root.setAttribute("WidthNumber", WidthNumber);
    root.setAttribute("HeightNumber", HeightNumber);
    root.setAttribute("CreatedOn", CreatedOn);
    root.setAttribute("LastChange", LastChange);
    root.setAttribute("Author", Author);
    root.setAttribute("Comment", Comment);
    root.setAttribute("TasksName", TasksName);
    doc.appendChild(root);

    networks->toXml(doc, root);
    components->toXml(doc, root);
    connections->toXml(doc, root);
    // 输出到文件
    QTextStream out_stream(&file);
    doc.save(out_stream, 4); // 缩进4格
    file.close();
}

void LDFile::fromXml(QString path)
{
    // 打开或创建文件
    QFile file(path);
    if (!file.open(QFile::ReadOnly))
    {
        return;
    }

    filePath = path;

    QDomDocument doc;
    if (!doc.setContent(&file))
    {
        file.close();
        return;
    }
    file.close();

    // 返回根节点
    QDomElement root = doc.documentElement();
    Name = root.attribute("Name");
    Type = root.attribute("Type");
    Code = root.attribute("Code");
    Version = root.attribute("Version");
    WidthNumber = root.attribute("WidthNumber").toInt();
    HeightNumber = root.attribute("HeightNumber").toInt();
    CreatedOn = root.attribute("CreatedOn");
    LastChange = root.attribute("LastChange");
    Author = root.attribute("Author");
    Comment = root.attribute("Comment");
    TasksName = root.attribute("TasksName");

    for (int i = 0; i < root.childNodes().size(); i++)
    {
        QDomNode node = root.childNodes().at(i);
        if (node.isElement()) // 如果节点是元素
        {
            if (node.nodeName() == "Networks")
            {
                networks->fromXml(node);
            }
            else if (node.nodeName() == "Components")
            {
                components->fromXml(node);
            }
            else if (node.nodeName() == "Connections")
            {
                connections->fromXml(node);
            }
        }
    }
    // 将元件对应到网络
    networkWithComponent();

    reSize();

    // 清理无效链接
    clearInvalidConnections();
}

QByteArray LDFile::toXml()
{
    // 清理无效链接
    clearInvalidConnections();

    // 重新计算下页面宽度和高度
    reSize();

    QDomDocument doc;
    QByteArray bytes;
    // 写入xml头部
    QDomProcessingInstruction instruction; // 添加处理命令
    instruction = doc.createProcessingInstruction("xml", "version=\"1.0\" encoding=\"UTF-8\"");
    doc.appendChild(instruction);
    // 添加根节点
    QDomElement root = doc.createElement("File");
    root.setAttribute("Name", Name);
    root.setAttribute("Type", Type);
    root.setAttribute("Code", Code);
    root.setAttribute("Version", Version);
    root.setAttribute("WidthNumber", WidthNumber);
    root.setAttribute("HeightNumber", HeightNumber);
    root.setAttribute("CreatedOn", CreatedOn);
    root.setAttribute("LastChange", LastChange);
    root.setAttribute("Author", Author);
    root.setAttribute("Comment", Comment);
    root.setAttribute("TasksName", TasksName);
    doc.appendChild(root);

    networks->toXml(doc, root);
    components->toXml(doc, root);
    connections->toXml(doc, root);

    QBuffer buffer(&bytes);
    buffer.open(QIODevice::WriteOnly);

    QTextStream out(&buffer);
    doc.save(out, 4); // 缩进 4 个空格

    return bytes;
}

void LDFile::fromXml(QByteArray bytes)
{
    QDomDocument doc;
    if (!doc.setContent(bytes))
    {
        return;
    }

    // 返回根节点
    QDomElement root = doc.documentElement();
    Name = root.attribute("Name");
    Type = root.attribute("Type");
    Code = root.attribute("Code");
    Version = root.attribute("Version");
    WidthNumber = root.attribute("WidthNumber").toInt();
    HeightNumber = root.attribute("HeightNumber").toInt();
    CreatedOn = root.attribute("CreatedOn");
    LastChange = root.attribute("LastChange");
    Author = root.attribute("Author");
    Comment = root.attribute("Comment");
    TasksName = root.attribute("TasksName");

    for (int i = 0; i < root.childNodes().size(); i++)
    {
        QDomNode node = root.childNodes().at(i);
        if (node.isElement()) // 如果节点是元素
        {
            if (node.nodeName() == "Networks")
            {
                networks->fromXml(node);
            }
            else if (node.nodeName() == "Components")
            {
                components->fromXml(node);
            }
            else if (node.nodeName() == "Connections")
            {
                connections->fromXml(node);
            }
        }
    }
    // 将元件对应到网络
    networkWithComponent();

    reSize();

    // 清理无效链接
    clearInvalidConnections();
}

void LDFile::networkWithComponent()
{
    // 先清除网络对应关系
    for (QSharedPointer<LDNetwork> &n_ptr : networks->networkMap)
    {
        n_ptr->componentMap.clear();
    }
    bool findNetwork;
    for (QSharedPointer<LDComponent> &c_ptr : components->componentMap)
    {
        findNetwork = false;
        for (QSharedPointer<LDNetwork> &n_ptr : networks->networkMap)
        {
            if (c_ptr->NetworkNumber == n_ptr->Number)
            {
                findNetwork = true;
                // 元件对应到网络
                c_ptr->network = n_ptr;
                // 网络对应到元件
                n_ptr->componentMap.insert(c_ptr->Number, c_ptr);
                break;
            }
        }
        if (!findNetwork)
        {
            // 元件没有找到对应的网络
            c_ptr->network = nullptr;
        }
    }
}

int LDFile::getMaxComonpentNumber()
{
    int max = 0;
    for (QSharedPointer<LDComponent> &c_ptr : components->componentMap)
    {
        if (c_ptr->Number > max)
        {
            max = c_ptr->Number;
        }
    }
    return max;
}

int LDFile::getMaxOrderNumbber(QString taskName)
{
    int max = 0;
    for (QSharedPointer<LDComponent> &c_ptr : components->componentMap)
    {
        if (c_ptr->TaskName == taskName && c_ptr->TaskOrderNumber > max)
        {
            max = c_ptr->TaskOrderNumber;
        }
    }
    return max;
}

void LDFile::clearInvalidConnections()
{
    for (QSharedPointer<LDConnection> &cn_ptr : connections->connectionList)
    {
        bool findSource = false;
        bool findTarget = false;
        // 判断该链接的路径能否找到对应的引脚
        if (components->componentMap.contains(cn_ptr->SourceComponentNumber) &&
            components->componentMap.contains(cn_ptr->TargetComponentNumber))
        {
            findSource = true;
            break;
        }

        // 没找到,则删除
        if (!(findSource && findTarget))
        {
            connections->connectionList.removeOne(cn_ptr);
        }
    }
}

QJsonArray LDFile::sortAllTaskOrderNumber()
{
    QJsonArray ary;
    int index = 1;
    for (QSharedPointer<LDNetwork> &network : networks->networkMap)
    {
        // 将非block元素的执行顺序置为0
        QList<QSharedPointer<LDComponent>> list;

        QMap<int, QSharedPointer<LDComponent>>::iterator itor;
        for (itor = network->componentMap.begin(); itor != network->componentMap.end(); itor++)
        {
            if (itor.value()->Type == "Block")
            {
                list << itor.value();
            }
            else
            {
                itor.value()->TaskOrderNumber = 0;
            }
        }
        std::sort(list.begin(), list.end(), [](QSharedPointer<LDComponent> &a, QSharedPointer<LDComponent> &b) {
            if (a->XPos < b->XPos)
            {
                return true;
            }
            else if (a->XPos == b->XPos)
            {
                return a->YPos < b->YPos;
            }
            else
            {
                return false;
            }
        });
        for (QSharedPointer<LDComponent> &com : list)
        {
            if (com->TaskOrderNumber != index)
            {
                QJsonObject obj;
                obj["NetworkNumber"] = com->NetworkNumber;
                obj["ComponentNumber"] = com->Number;
                obj["action"] = "modify";
                obj["name"] = "TaskOrderNumber";
                obj["value"] = index;
                ary.append(obj);
            }
            com->TaskOrderNumber = index;
            index++;
        }
    }
    return ary;
}

QSharedPointer<LDComponent> LDFile::searchComponentFromName(const QString name)
{
    for (auto &com : components->componentMap)
    {
        if (com->Name == name)
        {
            return com;
        }
    }
    return nullptr;
}

QSharedPointer<LDComponent> LDFile::searchComponentFromNumber(int number)
{
    if (components->componentMap.contains(number))
    {
        return components->componentMap.value(number);
    }
    return nullptr;
}

void LDFile::reSize()
{
    // 默认页面最小宽度
    int minWidth = 100;
    int heightNumber = 0;
    for (QSharedPointer<LDNetwork> &n_ptr : networks->networkMap)
    {
        // 默认网络最小高度
        int minHeight = 20;
        QMap<int, QSharedPointer<LDComponent>>::iterator itor;
        for (itor = n_ptr->componentMap.begin(); itor != n_ptr->componentMap.end(); itor++)
        {
            int comHeight = (itor.value()->Height + itor.value()->YPos);
            int comWidth = (itor.value()->Width + itor.value()->XPos);
            if (comHeight > minHeight)
            {
                minHeight = comHeight;
            }
            if (comWidth > minWidth)
            {
                minWidth = comWidth;
            }
        }
        n_ptr->Height = minHeight + 10;
        heightNumber += n_ptr->Height;
    }
    // 设定该页面的总宽度和高度
    WidthNumber = minWidth + 5;
    HeightNumber = heightNumber + 5;
}

bool LDFile::checkBlockOutConnection()
{
    bool flag = false;
    for (QSharedPointer<LDComponent> &com : components->componentMap)
    {
        // 如果是功能块则检查所有输出引脚是否都有链接
        if (com->Type == "Block")
        {
            for (QSharedPointer<LDConnector> &con : com->connectorList)
            {
                if (!con->IsLogical)
                {
                    flag = false;
                    for (QSharedPointer<LDConnection> &conn : connections->connectionList)
                    {
                        if ((conn->SourceComponentNumber == com->Number && conn->SourcePinId == con->PinId) ||
                            (conn->TargetComponentNumber == com->Number && conn->TargetPinId == con->PinId))
                        {
                            flag = true;
                            break;
                        }
                    }
                    if (!flag)
                    {
                        qDebug() << com->Number << con->PinId;
                        return true;
                    }
                }
            }
        }
        else if (com->Type == "Variable")
        {
            if (com->AuxContent == "???")
            {
                return true;
            }
        }
    }

    return false;
}

bool LDFile::checkCycle()
{
    QStringList result;

    QMap<int, QVector<int>> adj;
    // 遍历所有边
    for (QSharedPointer<LDConnection> &conn : connections->connectionList)
    {
        // 获取源 与目的元件 变量元件不统计
        QSharedPointer<LDComponent> sourceCom = searchComponentFromNumber(conn->SourceComponentNumber);
        if (sourceCom->Type != "Block")
        {
            continue;
        }
        QSharedPointer<LDComponent> targetCom = searchComponentFromNumber(conn->TargetComponentNumber);
        if (targetCom->Type != "Block")
        {
            continue;
        }
        if (adj.contains(conn->SourceComponentNumber))
        {
            QVector<int> oldline = adj[conn->SourceComponentNumber];
            if (!oldline.contains(conn->TargetComponentNumber))
            {
                oldline.push_back(conn->TargetComponentNumber);
            }
        }
        else
        {
            QVector<int> newline;
            newline.append(conn->TargetComponentNumber);
            adj.insert(conn->SourceComponentNumber, newline);
        }
    }
    qDebug() << "adj.size()" << adj.size();
    if (adj.size() > 0)
    {
        int V = adj.size();
        QVector<bool> visited(V, false);
        QVector<bool> recStack(V, false);

        // 调用递归辅助函数来检查不同DFS树中的循环
        for (int i = 0; i < V; i++)
        {
            if (!visited[i] && isCyclicUtil(adj, i, visited, recStack))
            {
                return true;
            }
        }
    }
    return false;
}

bool LDFile::isCyclicUtil(QMap<int, QVector<int>> &adj, int u, QVector<bool> &v_visited, QVector<bool> &v_recStack)
{
    if (!v_visited[u])
    {
        // 记录当前节点作为已访问的和递归堆栈的一部分
        v_visited[u] = true;
        v_recStack[u] = true;

        // 对与该顶点相邻的所有顶点进行循环
        for (int x : adj[u])
        {
            if (!v_visited[x] && isCyclicUtil(adj, x, v_visited, v_recStack))
            {
                return true;
            }
            else if (v_recStack[x])
            {
                return true;
            }
        }
    }
    // 从递归堆栈中删除顶点
    v_recStack[u] = false;
    return false;
}

QJsonObject LDFile::toJsonObject()
{
    QJsonObject obj;
    obj["Name"] = Name;
    obj["Type"] = Type;
    obj["Version"] = Version;
    obj["WidthNumber"] = WidthNumber;
    obj["HeightNumber"] = HeightNumber;
    obj["Code"] = Code;
    obj["CreatedOn"] = CreatedOn;
    obj["LastChange"] = LastChange;
    obj["Author"] = Author;
    obj["Comment"] = Comment;
    obj["TasksName"] = TasksName;

    return obj;
}